/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --accent-color: #e74c3c;
    --text-color: #2c3e50;
    --text-light: #7f8c8d;
    --bg-color: #ffffff;
    --bg-secondary: #f8f9fa;
    --border-color: #e9ecef;
    --shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    --shadow-hover: 0 4px 20px rgba(0, 0, 0, 0.15);
}

[data-theme="dark"] {
    --primary-color: #ecf0f1;
    --secondary-color: #3498db;
    --accent-color: #e74c3c;
    --text-color: #ecf0f1;
    --text-light: #bdc3c7;
    --bg-color: #2c3e50;
    --bg-secondary: #34495e;
    --border-color: #4a5f7a;
    --shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    --shadow-hover: 0 4px 20px rgba(0, 0, 0, 0.4);
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: var(--bg-color);
    transition: all 0.3s ease;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    background: var(--bg-color);
    box-shadow: var(--shadow);
    min-height: 100vh;
}

/* 头部样式 */
.header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
}

.profile-section {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.profile-image {
    font-size: 4rem;
    color: rgba(255, 255, 255, 0.9);
}

.profile-info .name {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.profile-info .title {
    font-size: 1.3rem;
    font-weight: 400;
    margin-bottom: 1rem;
    opacity: 0.9;
}

.contact-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 0.5rem;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.contact-item i {
    width: 16px;
    text-align: center;
}

.actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.btn-print, .btn-theme {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 0.7rem 1.2rem;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-print:hover, .btn-theme:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

/* 主要内容区域 */
.main-content {
    display: grid;
    grid-template-columns: 350px 1fr;
    min-height: calc(100vh - 200px);
}

/* 侧边栏样式 */
.sidebar {
    background: var(--bg-secondary);
    padding: 2rem;
    border-right: 1px solid var(--border-color);
}

/* 主内容区样式 */
.main {
    padding: 2rem;
}

/* 通用section样式 */
.section {
    margin-bottom: 2.5rem;
}

.section-title {
    display: flex;
    align-items: center;
    gap: 0.7rem;
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--secondary-color);
}

.section-title i {
    color: var(--secondary-color);
}

.section-content {
    line-height: 1.7;
}

/* 技能样式 */
.skills-grid {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.skill-category h4 {
    color: var(--secondary-color);
    margin-bottom: 0.7rem;
    font-size: 1rem;
}

.skill-list {
    list-style: none;
    display: flex;
    flex-direction: column;
    gap: 0.3rem;
}

.skill-list li {
    background: var(--bg-color);
    padding: 0.4rem 0.8rem;
    border-radius: 15px;
    font-size: 0.85rem;
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.skill-list li:hover {
    background: var(--secondary-color);
    color: white;
    transform: translateX(5px);
}

/* 教育背景样式 */
.education-item {
    background: var(--bg-color);
    padding: 1.2rem;
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.education-item h4 {
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.institution {
    color: var(--secondary-color);
    font-weight: 500;
    margin-bottom: 0.3rem;
}

.date {
    color: var(--text-light);
    font-size: 0.9rem;
}

/* 证书样式 */
.certificate-list {
    list-style: none;
}

.certificate-list li {
    background: var(--bg-color);
    padding: 0.7rem 1rem;
    margin-bottom: 0.5rem;
    border-radius: 6px;
    border-left: 4px solid var(--accent-color);
    transition: all 0.3s ease;
}

.certificate-list li:hover {
    transform: translateX(5px);
    box-shadow: var(--shadow);
}

/* 工作经验样式 */
.experience-item {
    background: var(--bg-secondary);
    padding: 2rem;
    border-radius: 10px;
    margin-bottom: 2rem;
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.experience-item:hover {
    box-shadow: var(--shadow-hover);
    transform: translateY(-2px);
}

.experience-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.position {
    color: var(--primary-color);
    font-size: 1.2rem;
    font-weight: 600;
}

.company {
    color: var(--secondary-color);
    font-weight: 500;
}

.responsibilities {
    list-style: none;
    padding-left: 0;
}

.responsibilities li {
    position: relative;
    padding-left: 1.5rem;
    margin-bottom: 0.5rem;
    color: var(--text-color);
}

.responsibilities li::before {
    content: "▶";
    position: absolute;
    left: 0;
    color: var(--secondary-color);
    font-size: 0.8rem;
}

/* 项目经验样式 */
.project-item {
    background: var(--bg-secondary);
    padding: 1.8rem;
    border-radius: 10px;
    margin-bottom: 1.5rem;
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.project-item:hover {
    box-shadow: var(--shadow-hover);
    transform: translateY(-2px);
}

.project-name {
    color: var(--primary-color);
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.project-date {
    color: var(--text-light);
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.project-description {
    margin-bottom: 1rem;
    line-height: 1.6;
}

.project-tech {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.tech-tag {
    background: var(--secondary-color);
    color: white;
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .main-content {
        grid-template-columns: 1fr;
    }
    
    .sidebar {
        order: 2;
    }
    
    .main {
        order: 1;
    }
    
    .header {
        flex-direction: column;
        text-align: center;
        gap: 1.5rem;
    }
    
    .profile-section {
        flex-direction: column;
        text-align: center;
    }
    
    .contact-info {
        grid-template-columns: 1fr;
        text-align: left;
    }
    
    .experience-header {
        flex-direction: column;
        align-items: flex-start;
    }
}

/* 打印样式 */
@media print {
    .actions {
        display: none;
    }
    
    .container {
        box-shadow: none;
    }
    
    .section {
        break-inside: avoid;
    }
    
    .experience-item, .project-item {
        break-inside: avoid;
    }
}
