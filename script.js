// 主题切换功能
function toggleTheme() {
    const body = document.body;
    const themeBtn = document.querySelector('.btn-theme i');
    
    if (body.getAttribute('data-theme') === 'dark') {
        body.removeAttribute('data-theme');
        themeBtn.className = 'fas fa-moon';
        localStorage.setItem('theme', 'light');
    } else {
        body.setAttribute('data-theme', 'dark');
        themeBtn.className = 'fas fa-sun';
        localStorage.setItem('theme', 'dark');
    }
}

// 页面加载时应用保存的主题
document.addEventListener('DOMContentLoaded', function() {
    const savedTheme = localStorage.getItem('theme');
    const themeBtn = document.querySelector('.btn-theme i');
    
    if (savedTheme === 'dark') {
        document.body.setAttribute('data-theme', 'dark');
        themeBtn.className = 'fas fa-sun';
    }
    
    // 添加平滑滚动效果
    addSmoothScrolling();
    
    // 添加技能动画
    addSkillAnimations();
    
    // 添加打字机效果
    addTypewriterEffect();
});

// 平滑滚动效果
function addSmoothScrolling() {
    // 为所有内部链接添加平滑滚动
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

// 技能动画效果
function addSkillAnimations() {
    const skillItems = document.querySelectorAll('.skill-list li');
    
    // 创建观察器
    const observer = new IntersectionObserver((entries) => {
        entries.forEach((entry, index) => {
            if (entry.isIntersecting) {
                setTimeout(() => {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }, index * 100);
            }
        });
    }, {
        threshold: 0.1
    });
    
    // 初始化技能项目样式并观察
    skillItems.forEach(item => {
        item.style.opacity = '0';
        item.style.transform = 'translateY(20px)';
        item.style.transition = 'all 0.6s ease';
        observer.observe(item);
    });
}

// 打字机效果（为标题添加）
function addTypewriterEffect() {
    const titleElement = document.querySelector('.title');
    if (titleElement) {
        const originalText = titleElement.textContent;
        titleElement.textContent = '';
        
        let i = 0;
        const typeWriter = () => {
            if (i < originalText.length) {
                titleElement.textContent += originalText.charAt(i);
                i++;
                setTimeout(typeWriter, 100);
            }
        };
        
        // 延迟开始打字机效果
        setTimeout(typeWriter, 1000);
    }
}

// 添加页面滚动时的导航高亮效果
window.addEventListener('scroll', function() {
    const sections = document.querySelectorAll('.section');
    const scrollPos = window.pageYOffset || document.documentElement.scrollTop;
    
    sections.forEach(section => {
        const sectionTop = section.offsetTop - 100;
        const sectionHeight = section.offsetHeight;
        const sectionId = section.getAttribute('id');
        
        if (scrollPos >= sectionTop && scrollPos < sectionTop + sectionHeight) {
            // 可以在这里添加导航高亮逻辑
            section.style.transform = 'scale(1.02)';
            section.style.transition = 'transform 0.3s ease';
        } else {
            section.style.transform = 'scale(1)';
        }
    });
});

// 添加鼠标悬停效果
document.addEventListener('DOMContentLoaded', function() {
    // 为项目卡片添加倾斜效果
    const projectItems = document.querySelectorAll('.project-item, .experience-item');
    
    projectItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px) rotateX(5deg)';
            this.style.transition = 'all 0.3s ease';
        });
        
        item.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) rotateX(0)';
        });
    });
});

// 添加联系信息点击功能
document.addEventListener('DOMContentLoaded', function() {
    const contactItems = document.querySelectorAll('.contact-item');
    
    contactItems.forEach(item => {
        item.addEventListener('click', function() {
            const text = this.querySelector('span').textContent;
            
            // 复制到剪贴板
            if (navigator.clipboard) {
                navigator.clipboard.writeText(text).then(() => {
                    showNotification('已复制到剪贴板: ' + text);
                });
            }
        });
        
        // 添加点击提示
        item.style.cursor = 'pointer';
        item.title = '点击复制';
    });
});

// 显示通知
function showNotification(message) {
    // 创建通知元素
    const notification = document.createElement('div');
    notification.textContent = message;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: var(--secondary-color);
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 5px;
        box-shadow: var(--shadow);
        z-index: 1000;
        opacity: 0;
        transform: translateY(-20px);
        transition: all 0.3s ease;
    `;
    
    document.body.appendChild(notification);
    
    // 显示动画
    setTimeout(() => {
        notification.style.opacity = '1';
        notification.style.transform = 'translateY(0)';
    }, 100);
    
    // 自动隐藏
    setTimeout(() => {
        notification.style.opacity = '0';
        notification.style.transform = 'translateY(-20px)';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// 添加键盘快捷键
document.addEventListener('keydown', function(e) {
    // Ctrl/Cmd + P 打印
    if ((e.ctrlKey || e.metaKey) && e.key === 'p') {
        e.preventDefault();
        window.print();
    }
    
    // Ctrl/Cmd + D 切换主题
    if ((e.ctrlKey || e.metaKey) && e.key === 'd') {
        e.preventDefault();
        toggleTheme();
    }
});

// 添加页面加载动画
window.addEventListener('load', function() {
    const container = document.querySelector('.container');
    container.style.opacity = '0';
    container.style.transform = 'translateY(20px)';
    container.style.transition = 'all 0.8s ease';
    
    setTimeout(() => {
        container.style.opacity = '1';
        container.style.transform = 'translateY(0)';
    }, 100);
});

// 导出PDF功能（需要额外的库支持）
function exportToPDF() {
    // 这里可以集成 jsPDF 或其他PDF生成库
    showNotification('PDF导出功能需要额外配置');
}

// 添加简历数据编辑功能
function enableEditMode() {
    const editableElements = document.querySelectorAll('.name, .title, .contact-item span, .section-content p, .responsibilities li');
    
    editableElements.forEach(element => {
        element.contentEditable = true;
        element.style.border = '1px dashed var(--secondary-color)';
        element.style.padding = '2px 4px';
    });
    
    showNotification('编辑模式已启用，点击文本即可编辑');
}

// 保存编辑内容到本地存储
function saveToLocalStorage() {
    const resumeData = {
        name: document.querySelector('.name').textContent,
        title: document.querySelector('.title').textContent,
        // 可以添加更多字段
    };
    
    localStorage.setItem('resumeData', JSON.stringify(resumeData));
    showNotification('简历数据已保存');
}

// 从本地存储加载数据
function loadFromLocalStorage() {
    const savedData = localStorage.getItem('resumeData');
    if (savedData) {
        const resumeData = JSON.parse(savedData);
        
        if (resumeData.name) {
            document.querySelector('.name').textContent = resumeData.name;
        }
        if (resumeData.title) {
            document.querySelector('.title').textContent = resumeData.title;
        }
        // 可以添加更多字段的加载逻辑
    }
}
