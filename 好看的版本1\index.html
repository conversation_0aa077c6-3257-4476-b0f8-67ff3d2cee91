<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>高级测试工程师简历</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <!-- 头部信息 -->
        <header class="header">
            <div class="profile-section">
                <div class="profile-image">
                    <i class="fas fa-user-circle"></i>
                </div>
                <div class="profile-info">
                    <h1 class="name">您的姓名</h1>
                    <h2 class="title">高级测试工程师</h2>
                    <div class="contact-info">
                        <div class="contact-item">
                            <i class="fas fa-phone"></i>
                            <span>手机号码</span>
                        </div>
                        <div class="contact-item">
                            <i class="fas fa-envelope"></i>
                            <span>邮箱地址</span>
                        </div>
                        <div class="contact-item">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>所在城市</span>
                        </div>
                        <div class="contact-item">
                            <i class="fab fa-linkedin"></i>
                            <span>LinkedIn</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="actions">
                <button class="btn-print" onclick="window.print()">
                    <i class="fas fa-print"></i> 打印简历
                </button>
                <button class="btn-theme" onclick="toggleTheme()">
                    <i class="fas fa-moon"></i> 切换主题
                </button>
            </div>
        </header>

        <div class="main-content">
            <!-- 左侧栏 -->
            <aside class="sidebar">
                <!-- 专业总结 -->
                <section class="section">
                    <h3 class="section-title">
                        <i class="fas fa-user"></i>
                        专业总结
                    </h3>
                    <div class="section-content">
                        <p>具有X年软件测试经验的高级测试工程师，专注于自动化测试、性能测试和质量保证。熟练掌握多种测试工具和框架，具备丰富的项目管理和团队协作经验。</p>
                    </div>
                </section>

                <!-- 核心技能 -->
                <section class="section">
                    <h3 class="section-title">
                        <i class="fas fa-cogs"></i>
                        核心技能
                    </h3>
                    <div class="section-content">
                        <div class="skills-grid">
                            <div class="skill-category">
                                <h4>测试技术</h4>
                                <ul class="skill-list">
                                    <li>自动化测试</li>
                                    <li>性能测试</li>
                                    <li>接口测试</li>
                                    <li>移动端测试</li>
                                </ul>
                            </div>
                            <div class="skill-category">
                                <h4>测试工具</h4>
                                <ul class="skill-list">
                                    <li>Selenium</li>
                                    <li>JMeter</li>
                                    <li>Postman</li>
                                    <li>TestNG</li>
                                </ul>
                            </div>
                            <div class="skill-category">
                                <h4>编程语言</h4>
                                <ul class="skill-list">
                                    <li>Java</li>
                                    <li>Python</li>
                                    <li>JavaScript</li>
                                    <li>SQL</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- 教育背景 -->
                <section class="section">
                    <h3 class="section-title">
                        <i class="fas fa-graduation-cap"></i>
                        教育背景
                    </h3>
                    <div class="section-content">
                        <div class="education-item">
                            <h4>学位名称</h4>
                            <p class="institution">学校名称</p>
                            <p class="date">毕业时间</p>
                        </div>
                    </div>
                </section>

                <!-- 证书认证 -->
                <section class="section">
                    <h3 class="section-title">
                        <i class="fas fa-certificate"></i>
                        证书认证
                    </h3>
                    <div class="section-content">
                        <ul class="certificate-list">
                            <li>ISTQB认证测试工程师</li>
                            <li>软件测试工程师认证</li>
                            <li>敏捷测试认证</li>
                        </ul>
                    </div>
                </section>
            </aside>

            <!-- 主要内容区 -->
            <main class="main">
                <!-- 工作经验 -->
                <section class="section">
                    <h3 class="section-title">
                        <i class="fas fa-briefcase"></i>
                        工作经验
                    </h3>
                    <div class="section-content">
                        <div class="experience-item">
                            <div class="experience-header">
                                <h4 class="position">高级测试工程师</h4>
                                <span class="company">公司名称</span>
                                <span class="date">2020.01 - 至今</span>
                            </div>
                            <ul class="responsibilities">
                                <li>负责制定和执行测试策略，确保产品质量</li>
                                <li>设计和维护自动化测试框架，提高测试效率</li>
                                <li>进行性能测试和安全测试，识别系统瓶颈</li>
                                <li>指导初级测试工程师，提升团队整体能力</li>
                            </ul>
                        </div>

                        <div class="experience-item">
                            <div class="experience-header">
                                <h4 class="position">测试工程师</h4>
                                <span class="company">公司名称</span>
                                <span class="date">2018.06 - 2019.12</span>
                            </div>
                            <ul class="responsibilities">
                                <li>执行功能测试、集成测试和回归测试</li>
                                <li>编写和维护测试用例，确保测试覆盖率</li>
                                <li>参与需求分析和设计评审</li>
                                <li>跟踪和管理缺陷，与开发团队协作解决问题</li>
                            </ul>
                        </div>
                    </div>
                </section>

                <!-- 项目经验 -->
                <section class="section">
                    <h3 class="section-title">
                        <i class="fas fa-project-diagram"></i>
                        项目经验
                    </h3>
                    <div class="section-content">
                        <div class="project-item">
                            <h4 class="project-name">电商平台测试项目</h4>
                            <p class="project-date">2022.03 - 2022.12</p>
                            <p class="project-description">
                                负责大型电商平台的全面测试，包括Web端、移动端和API测试。
                                建立了完整的自动化测试体系，测试覆盖率达到85%以上。
                            </p>
                            <div class="project-tech">
                                <span class="tech-tag">Selenium</span>
                                <span class="tech-tag">Appium</span>
                                <span class="tech-tag">JMeter</span>
                                <span class="tech-tag">Jenkins</span>
                            </div>
                        </div>

                        <div class="project-item">
                            <h4 class="project-name">金融系统性能优化</h4>
                            <p class="project-date">2021.08 - 2022.02</p>
                            <p class="project-description">
                                对核心金融系统进行性能测试和优化，通过压力测试发现性能瓶颈，
                                协助开发团队优化系统架构，系统响应时间提升40%。
                            </p>
                            <div class="project-tech">
                                <span class="tech-tag">LoadRunner</span>
                                <span class="tech-tag">JProfiler</span>
                                <span class="tech-tag">MySQL</span>
                                <span class="tech-tag">Redis</span>
                            </div>
                        </div>
                    </div>
                </section>
            </main>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
